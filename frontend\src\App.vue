<script setup>
import HelloWorld from './components/HelloWorld.vue'
import {Shutdown} from '../../wailsjs/go/main/App'
</script>

<template>
  <img id="logo" alt="Wails logo" draggable="false" src="./assets/images/logo-universal.png"/>
  <HelloWorld/>
  <button @click="Shutdown">Shutdown</button>
</template>

<style>
#logo {
  display: block;
  width: 50%;
  height: 50%;
  margin: auto;
  padding: 10% 0 0;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-origin: content-box;

  --wails-draggable: drag;
  -webkit-app-region: drag;
  user-select: none;
}
</style>
